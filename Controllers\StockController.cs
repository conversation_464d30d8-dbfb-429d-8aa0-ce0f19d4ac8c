using Microsoft.AspNetCore.Mvc;
using microgest.Models;
using microgest.Services.Interfaces;
using microgest.Enums;

namespace microgest.Controllers;

/// <summary>
/// Controller for managing stock operations
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class StockController : ControllerBase
{
    private readonly IStockService _stockService;

    /// <summary>
    /// Initializes a new instance of the <see cref="StockController"/> class
    /// </summary>
    /// <param name="stockService">The stock service</param>
    public StockController(IStockService stockService)
    {
        _stockService = stockService ?? throw new ArgumentNullException(nameof(stockService));
    }

    /// <summary>
    /// Gets the current stock level for a product
    /// </summary>
    /// <param name="productId">The product ID</param>
    /// <returns>The current stock level</returns>
    [HttpGet("level/{productId}")]
    public async Task<ActionResult<int>> GetStockLevel(int productId)
    {
        try
        {
            var stockLevel = await _stockService.GetStockLevelAsync(productId);
            return Ok(stockLevel);
        }
        catch (InvalidOperationException ex)
        {
            return NotFound(ex.Message);
        }
    }

    /// <summary>
    /// Gets stock movement history for a product
    /// </summary>
    /// <param name="productId">The product ID</param>
    /// <param name="skip">Number of records to skip</param>
    /// <param name="take">Number of records to take</param>
    /// <returns>A collection of stock movements</returns>
    [HttpGet("history/{productId}")]
    public async Task<ActionResult<IEnumerable<StockMovement>>> GetStockHistory(
        int productId,
        [FromQuery] int skip = 0,
        [FromQuery] int take = 100)
    {
        var history = await _stockService.GetStockHistoryAsync(productId, skip, take);
        return Ok(history);
    }

    /// <summary>
    /// Gets stock movements within a date range
    /// </summary>
    /// <param name="startDate">The start date</param>
    /// <param name="endDate">The end date</param>
    /// <param name="movementType">Optional movement type filter</param>
    /// <returns>A collection of stock movements</returns>
    [HttpGet("movements")]
    public async Task<ActionResult<IEnumerable<StockMovement>>> GetStockMovements(
        [FromQuery] DateTime startDate,
        [FromQuery] DateTime endDate,
        [FromQuery] MovementType? movementType = null)
    {
        var movements = await _stockService.GetStockMovementsByDateRangeAsync(startDate, endDate, movementType);
        return Ok(movements);
    }

    /// <summary>
    /// Gets products with low stock
    /// </summary>
    /// <returns>A collection of products with low stock</returns>
    [HttpGet("low-stock")]
    public async Task<ActionResult<IEnumerable<Product>>> GetLowStockProducts()
    {
        var products = await _stockService.CheckLowStockAsync();
        return Ok(products);
    }

    /// <summary>
    /// Adds stock to a product
    /// </summary>
    /// <param name="request">The stock addition request</param>
    /// <returns>The created stock movement</returns>
    [HttpPost("add")]
    public async Task<ActionResult<StockMovement>> AddStock([FromBody] AddStockRequest request)
    {
        try
        {
            var stockMovement = await _stockService.AddStockAsync(
                request.ProductId, 
                request.Quantity, 
                request.Reason, 
                request.UserId);
            
            return Ok(stockMovement);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return NotFound(ex.Message);
        }
    }

    /// <summary>
    /// Removes stock from a product
    /// </summary>
    /// <param name="request">The stock removal request</param>
    /// <returns>The created stock movement</returns>
    [HttpPost("remove")]
    public async Task<ActionResult<StockMovement>> RemoveStock([FromBody] RemoveStockRequest request)
    {
        try
        {
            var stockMovement = await _stockService.RemoveStockAsync(
                request.ProductId, 
                request.Quantity, 
                request.Reason, 
                request.UserId);
            
            return Ok(stockMovement);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Adjusts stock for a product
    /// </summary>
    /// <param name="request">The stock adjustment request</param>
    /// <returns>The created stock movement</returns>
    [HttpPost("adjust")]
    public async Task<ActionResult<StockMovement>> AdjustStock([FromBody] AdjustStockRequest request)
    {
        try
        {
            var stockMovement = await _stockService.AdjustStockAsync(
                request.ProductId, 
                request.NewQuantity, 
                request.Reason, 
                request.UserId);
            
            return Ok(stockMovement);
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ex.Message);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Checks if sufficient stock is available
    /// </summary>
    /// <param name="productId">The product ID</param>
    /// <param name="quantity">The quantity to check</param>
    /// <returns>True if sufficient stock is available</returns>
    [HttpGet("available/{productId}/{quantity}")]
    public async Task<ActionResult<bool>> IsStockAvailable(int productId, int quantity)
    {
        var isAvailable = await _stockService.IsStockAvailableAsync(productId, quantity);
        return Ok(isAvailable);
    }
}

/// <summary>
/// Request model for adding stock
/// </summary>
public class AddStockRequest
{
    public int ProductId { get; set; }
    public int Quantity { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string? UserId { get; set; }
}

/// <summary>
/// Request model for removing stock
/// </summary>
public class RemoveStockRequest
{
    public int ProductId { get; set; }
    public int Quantity { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string? UserId { get; set; }
}

/// <summary>
/// Request model for adjusting stock
/// </summary>
public class AdjustStockRequest
{
    public int ProductId { get; set; }
    public int NewQuantity { get; set; }
    public string Reason { get; set; } = string.Empty;
    public string? UserId { get; set; }
}
