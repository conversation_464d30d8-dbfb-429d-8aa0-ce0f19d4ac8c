using Microsoft.AspNetCore.Mvc;
using microgest.Models;
using microgest.Services.Interfaces;

namespace microgest.Controllers;

/// <summary>
/// Controller for managing suppliers
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class SuppliersController : ControllerBase
{
    private readonly ISupplierService _supplierService;

    /// <summary>
    /// Initializes a new instance of the <see cref="SuppliersController"/> class
    /// </summary>
    /// <param name="supplierService">The supplier service</param>
    public SuppliersController(ISupplierService supplierService)
    {
        _supplierService = supplierService ?? throw new ArgumentNullException(nameof(supplierService));
    }

    /// <summary>
    /// Gets all suppliers
    /// </summary>
    /// <returns>A collection of suppliers</returns>
    [HttpGet]
    public async Task<ActionResult<IEnumerable<Supplier>>> GetSuppliers()
    {
        var suppliers = await _supplierService.GetSuppliersAsync();
        return Ok(suppliers);
    }

    /// <summary>
    /// Gets a specific supplier by ID
    /// </summary>
    /// <param name="id">The supplier ID</param>
    /// <returns>The supplier if found</returns>
    [HttpGet("{id}")]
    public async Task<ActionResult<Supplier>> GetSupplier(int id)
    {
        var supplier = await _supplierService.GetSupplierByIdAsync(id);

        if (supplier == null)
        {
            return NotFound($"Supplier with ID {id} not found.");
        }

        return Ok(supplier);
    }

    /// <summary>
    /// Creates a new supplier
    /// </summary>
    /// <param name="supplier">The supplier to create</param>
    /// <returns>The created supplier</returns>
    [HttpPost]
    public async Task<ActionResult<Supplier>> CreateSupplier(Supplier supplier)
    {
        try
        {
            var createdSupplier = await _supplierService.CreateSupplierAsync(supplier);
            return CreatedAtAction(nameof(GetSupplier), new { id = createdSupplier.Id }, createdSupplier);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Updates an existing supplier
    /// </summary>
    /// <param name="id">The supplier ID</param>
    /// <param name="supplier">The updated supplier data</param>
    /// <returns>The updated supplier</returns>
    [HttpPut("{id}")]
    public async Task<ActionResult<Supplier>> UpdateSupplier(int id, Supplier supplier)
    {
        if (id != supplier.Id)
        {
            return BadRequest("Supplier ID mismatch.");
        }

        try
        {
            var updatedSupplier = await _supplierService.UpdateSupplierAsync(supplier);
            return Ok(updatedSupplier);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Deletes a supplier
    /// </summary>
    /// <param name="id">The supplier ID</param>
    /// <returns>No content if successful</returns>
    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteSupplier(int id)
    {
        var deleted = await _supplierService.DeleteSupplierAsync(id);
        if (!deleted)
        {
            return NotFound($"Supplier with ID {id} not found.");
        }

        return NoContent();
    }
}
