using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using microgest.Models;

namespace microgest.Data.Configurations;

/// <summary>
/// Entity configuration for the StockMovement entity
/// </summary>
public class StockMovementConfiguration : IEntityTypeConfiguration<StockMovement>
{
    /// <summary>
    /// Configures the StockMovement entity
    /// </summary>
    /// <param name="builder">The entity type builder</param>
    public void Configure(EntityTypeBuilder<StockMovement> builder)
    {
        builder.ToTable("StockMovements");

        builder.HasKey(sm => sm.Id);

        builder.Property(sm => sm.MovementType)
            .IsRequired()
            .HasConversion<int>();

        builder.Property(sm => sm.Quantity)
            .IsRequired();

        builder.Property(sm => sm.Reason)
            .IsRequired()
            .HasMaxLength(500);

        builder.Property(sm => sm.Date)
            .IsRequired();

        builder.Property(sm => sm.UserId)
            .HasMaxLength(100);

        builder.HasIndex(sm => sm.Date);
        builder.HasIndex(sm => sm.ProductId);

        // Configure relationships
        builder.HasOne(sm => sm.Product)
            .WithMany(p => p.StockMovements)
            .HasForeignKey(sm => sm.ProductId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
