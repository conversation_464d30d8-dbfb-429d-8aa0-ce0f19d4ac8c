using Microsoft.EntityFrameworkCore;
using microgest.Models;
using microgest.Data.Configurations;

namespace microgest.Data;

/// <summary>
/// Database context for the Microgest application
/// </summary>
public class MicrogestDbContext : DbContext
{
    /// <summary>
    /// Initializes a new instance of the <see cref="MicrogestDbContext"/> class
    /// </summary>
    /// <param name="options">The database context options</param>
    public MicrogestDbContext(DbContextOptions<MicrogestDbContext> options) : base(options)
    {
    }

    /// <summary>
    /// Gets or sets the Products DbSet
    /// </summary>
    public DbSet<Product> Products { get; set; }

    /// <summary>
    /// Gets or sets the Categories DbSet
    /// </summary>
    public DbSet<Category> Categories { get; set; }

    /// <summary>
    /// Gets or sets the StockMovements DbSet
    /// </summary>
    public DbSet<StockMovement> StockMovements { get; set; }

    /// <summary>
    /// Gets or sets the Suppliers DbSet
    /// </summary>
    public DbSet<Supplier> Suppliers { get; set; }

    /// <summary>
    /// Configures the model that was discovered by convention from the entity types
    /// </summary>
    /// <param name="modelBuilder">The builder being used to construct the model for this context</param>
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Apply entity configurations
        modelBuilder.ApplyConfiguration(new ProductConfiguration());
        modelBuilder.ApplyConfiguration(new CategoryConfiguration());
        modelBuilder.ApplyConfiguration(new StockMovementConfiguration());
        modelBuilder.ApplyConfiguration(new SupplierConfiguration());
    }

    /// <summary>
    /// Saves all changes made in this context to the database
    /// </summary>
    /// <returns>The number of state entries written to the database</returns>
    public override int SaveChanges()
    {
        UpdateTimestamps();
        return base.SaveChanges();
    }

    /// <summary>
    /// Saves all changes made in this context to the database asynchronously
    /// </summary>
    /// <param name="cancellationToken">A cancellation token to observe while waiting for the task to complete</param>
    /// <returns>A task that represents the asynchronous save operation</returns>
    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        UpdateTimestamps();
        return await base.SaveChangesAsync(cancellationToken);
    }

    /// <summary>
    /// Updates the timestamps for entities being added or modified
    /// </summary>
    private void UpdateTimestamps()
    {
        var entries = ChangeTracker.Entries<Product>()
            .Where(e => e.State == EntityState.Added || e.State == EntityState.Modified);

        foreach (var entry in entries)
        {
            if (entry.State == EntityState.Added)
            {
                entry.Entity.CreatedAt = DateTime.UtcNow;
            }
            entry.Entity.UpdatedAt = DateTime.UtcNow;
        }
    }
}
