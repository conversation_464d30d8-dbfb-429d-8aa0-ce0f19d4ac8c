namespace microgest.Enums;

/// <summary>
/// Represents the type of stock movement
/// </summary>
public enum MovementType
{
    /// <summary>
    /// Stock incoming (purchase, return, etc.)
    /// </summary>
    In = 1,

    /// <summary>
    /// Stock outgoing (sale, damage, etc.)
    /// </summary>
    Out = 2,

    /// <summary>
    /// Stock adjustment (inventory correction)
    /// </summary>
    Adjustment = 3
}
