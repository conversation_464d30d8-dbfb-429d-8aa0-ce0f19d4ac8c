using System.ComponentModel.DataAnnotations;

namespace microgest.Models;

/// <summary>
/// Represents a product category
/// </summary>
public class Category
{
    /// <summary>
    /// Gets or sets the unique identifier for the category
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Gets or sets the category name
    /// </summary>
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the category description
    /// </summary>
    [StringLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Gets or sets the collection of products in this category
    /// </summary>
    public virtual ICollection<Product> Products { get; set; } = new List<Product>();
}
