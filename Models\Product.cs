using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace microgest.Models;

/// <summary>
/// Represents a product in the inventory
/// </summary>
public class Product
{
    /// <summary>
    /// Gets or sets the unique identifier for the product
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Gets or sets the product name
    /// </summary>
    [Required]
    [StringLength(200)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the product description
    /// </summary>
    [StringLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Gets or sets the Stock Keeping Unit (SKU)
    /// </summary>
    [Required]
    [StringLength(50)]
    public string SKU { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the category identifier
    /// </summary>
    public int CategoryId { get; set; }

    /// <summary>
    /// Gets or sets the unit price of the product
    /// </summary>
    [Column(TypeName = "decimal(18,2)")]
    [Range(0, double.MaxValue, ErrorMessage = "Unit price must be a positive value")]
    public decimal UnitPrice { get; set; }

    /// <summary>
    /// Gets or sets the minimum stock level for this product
    /// </summary>
    [Range(0, int.MaxValue, ErrorMessage = "Minimum stock must be a non-negative value")]
    public int MinimumStock { get; set; }

    /// <summary>
    /// Gets or sets the current stock level for this product
    /// </summary>
    [Range(0, int.MaxValue, ErrorMessage = "Current stock must be a non-negative value")]
    public int CurrentStock { get; set; }

    /// <summary>
    /// Gets or sets the supplier identifier
    /// </summary>
    public int? SupplierId { get; set; }

    /// <summary>
    /// Gets or sets the date when the product was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets the date when the product was last updated
    /// </summary>
    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets the category navigation property
    /// </summary>
    public virtual Category Category { get; set; } = null!;

    /// <summary>
    /// Gets or sets the supplier navigation property
    /// </summary>
    public virtual Supplier? Supplier { get; set; }

    /// <summary>
    /// Gets or sets the collection of stock movements for this product
    /// </summary>
    public virtual ICollection<StockMovement> StockMovements { get; set; } = new List<StockMovement>();
}
