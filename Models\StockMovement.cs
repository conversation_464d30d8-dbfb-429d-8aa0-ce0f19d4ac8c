using System.ComponentModel.DataAnnotations;
using microgest.Enums;

namespace microgest.Models;

/// <summary>
/// Represents a stock movement record
/// </summary>
public class StockMovement
{
    /// <summary>
    /// Gets or sets the unique identifier for the stock movement
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Gets or sets the product identifier
    /// </summary>
    public int ProductId { get; set; }

    /// <summary>
    /// Gets or sets the type of movement (In, Out, Adjustment)
    /// </summary>
    [Required]
    public MovementType MovementType { get; set; }

    /// <summary>
    /// Gets or sets the quantity moved
    /// </summary>
    [Range(1, int.MaxValue, ErrorMessage = "Quantity must be a positive value")]
    public int Quantity { get; set; }

    /// <summary>
    /// Gets or sets the reason for the stock movement
    /// </summary>
    [Required]
    [StringLength(500)]
    public string Reason { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the date of the movement
    /// </summary>
    public DateTime Date { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Gets or sets the user identifier who performed the movement
    /// </summary>
    [StringLength(100)]
    public string? UserId { get; set; }

    /// <summary>
    /// Gets or sets the product navigation property
    /// </summary>
    public virtual Product Product { get; set; } = null!;
}
