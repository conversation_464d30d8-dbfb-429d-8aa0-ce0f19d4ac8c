using System.ComponentModel.DataAnnotations;

namespace microgest.Models;

/// <summary>
/// Represents a supplier
/// </summary>
public class Supplier
{
    /// <summary>
    /// Gets or sets the unique identifier for the supplier
    /// </summary>
    public int Id { get; set; }

    /// <summary>
    /// Gets or sets the supplier name
    /// </summary>
    [Required]
    [StringLength(200)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the supplier contact information
    /// </summary>
    [StringLength(500)]
    public string? ContactInfo { get; set; }

    /// <summary>
    /// Gets or sets the supplier email address
    /// </summary>
    [EmailAddress]
    [StringLength(100)]
    public string? Email { get; set; }

    /// <summary>
    /// Gets or sets the supplier phone number
    /// </summary>
    [Phone]
    [StringLength(20)]
    public string? Phone { get; set; }

    /// <summary>
    /// Gets or sets the collection of products from this supplier
    /// </summary>
    public virtual ICollection<Product> Products { get; set; } = new List<Product>();
}
