using Microsoft.EntityFrameworkCore;
using microgest.Data;
using microgest.Models;
using microgest.Services.Interfaces;

namespace microgest.Services;

/// <summary>
/// Service for managing category operations
/// </summary>
public class CategoryService : ICategoryService
{
    private readonly MicrogestDbContext _context;

    /// <summary>
    /// Initializes a new instance of the <see cref="CategoryService"/> class
    /// </summary>
    /// <param name="context">The database context</param>
    public CategoryService(MicrogestDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    /// <summary>
    /// Creates a new category
    /// </summary>
    /// <param name="category">The category to create</param>
    /// <returns>The created category</returns>
    /// <exception cref="ArgumentNullException">Thrown when category is null</exception>
    /// <exception cref="InvalidOperationException">Thrown when a category with the same name already exists</exception>
    public async Task<Category> CreateCategoryAsync(Category category)
    {
        if (category == null)
            throw new ArgumentNullException(nameof(category));

        // Check if category with same name already exists
        var existingCategory = await _context.Categories
            .FirstOrDefaultAsync(c => c.Name.ToLower() == category.Name.ToLower());

        if (existingCategory != null)
            throw new InvalidOperationException($"A category with the name '{category.Name}' already exists.");

        _context.Categories.Add(category);
        await _context.SaveChangesAsync();

        return category;
    }

    /// <summary>
    /// Updates an existing category
    /// </summary>
    /// <param name="category">The category to update</param>
    /// <returns>The updated category</returns>
    /// <exception cref="ArgumentNullException">Thrown when category is null</exception>
    /// <exception cref="InvalidOperationException">Thrown when category is not found or name conflict exists</exception>
    public async Task<Category> UpdateCategoryAsync(Category category)
    {
        if (category == null)
            throw new ArgumentNullException(nameof(category));

        var existingCategory = await _context.Categories.FindAsync(category.Id);
        if (existingCategory == null)
            throw new InvalidOperationException($"Category with ID {category.Id} not found.");

        // Check if another category with the same name exists
        var duplicateCategory = await _context.Categories
            .FirstOrDefaultAsync(c => c.Name.ToLower() == category.Name.ToLower() && c.Id != category.Id);

        if (duplicateCategory != null)
            throw new InvalidOperationException($"A category with the name '{category.Name}' already exists.");

        existingCategory.Name = category.Name;
        existingCategory.Description = category.Description;

        await _context.SaveChangesAsync();

        return existingCategory;
    }

    /// <summary>
    /// Gets all categories
    /// </summary>
    /// <returns>A collection of all categories</returns>
    public async Task<IEnumerable<Category>> GetCategoriesAsync()
    {
        return await _context.Categories
            .OrderBy(c => c.Name)
            .ToListAsync();
    }

    /// <summary>
    /// Gets a category by its identifier
    /// </summary>
    /// <param name="id">The category identifier</param>
    /// <returns>The category if found, null otherwise</returns>
    public async Task<Category?> GetCategoryByIdAsync(int id)
    {
        return await _context.Categories
            .Include(c => c.Products)
            .FirstOrDefaultAsync(c => c.Id == id);
    }

    /// <summary>
    /// Deletes a category
    /// </summary>
    /// <param name="id">The category identifier</param>
    /// <returns>True if the category was deleted, false otherwise</returns>
    /// <exception cref="InvalidOperationException">Thrown when category has associated products</exception>
    public async Task<bool> DeleteCategoryAsync(int id)
    {
        var category = await _context.Categories
            .Include(c => c.Products)
            .FirstOrDefaultAsync(c => c.Id == id);

        if (category == null)
            return false;

        // Check if category has products
        if (category.Products.Any())
            throw new InvalidOperationException("Cannot delete category that has associated products.");

        _context.Categories.Remove(category);
        await _context.SaveChangesAsync();

        return true;
    }

    /// <summary>
    /// Checks if a category exists
    /// </summary>
    /// <param name="id">The category identifier</param>
    /// <returns>True if the category exists, false otherwise</returns>
    public async Task<bool> CategoryExistsAsync(int id)
    {
        return await _context.Categories.AnyAsync(c => c.Id == id);
    }
}
