using microgest.Models;

namespace microgest.Services.Interfaces;

/// <summary>
/// Interface for category service operations
/// </summary>
public interface ICategoryService
{
    /// <summary>
    /// Creates a new category
    /// </summary>
    /// <param name="category">The category to create</param>
    /// <returns>The created category</returns>
    Task<Category> CreateCategoryAsync(Category category);

    /// <summary>
    /// Updates an existing category
    /// </summary>
    /// <param name="category">The category to update</param>
    /// <returns>The updated category</returns>
    Task<Category> UpdateCategoryAsync(Category category);

    /// <summary>
    /// Gets all categories
    /// </summary>
    /// <returns>A collection of all categories</returns>
    Task<IEnumerable<Category>> GetCategoriesAsync();

    /// <summary>
    /// Gets a category by its identifier
    /// </summary>
    /// <param name="id">The category identifier</param>
    /// <returns>The category if found, null otherwise</returns>
    Task<Category?> GetCategoryByIdAsync(int id);

    /// <summary>
    /// Deletes a category
    /// </summary>
    /// <param name="id">The category identifier</param>
    /// <returns>True if the category was deleted, false otherwise</returns>
    Task<bool> DeleteCategoryAsync(int id);

    /// <summary>
    /// Checks if a category exists
    /// </summary>
    /// <param name="id">The category identifier</param>
    /// <returns>True if the category exists, false otherwise</returns>
    Task<bool> CategoryExistsAsync(int id);
}
