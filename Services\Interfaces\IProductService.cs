using microgest.Models;

namespace microgest.Services.Interfaces;

/// <summary>
/// Interface for product service operations
/// </summary>
public interface IProductService
{
    /// <summary>
    /// Creates a new product
    /// </summary>
    /// <param name="product">The product to create</param>
    /// <returns>The created product</returns>
    Task<Product> CreateProductAsync(Product product);

    /// <summary>
    /// Updates an existing product
    /// </summary>
    /// <param name="product">The product to update</param>
    /// <returns>The updated product</returns>
    Task<Product> UpdateProductAsync(Product product);

    /// <summary>
    /// Gets a product by its identifier
    /// </summary>
    /// <param name="id">The product identifier</param>
    /// <returns>The product if found, null otherwise</returns>
    Task<Product?> GetProductAsync(int id);

    /// <summary>
    /// Gets all products with optional filtering and pagination
    /// </summary>
    /// <param name="categoryId">Optional category filter</param>
    /// <param name="supplierId">Optional supplier filter</param>
    /// <param name="searchTerm">Optional search term for name or SKU</param>
    /// <param name="skip">Number of records to skip</param>
    /// <param name="take">Number of records to take</param>
    /// <returns>A collection of products</returns>
    Task<IEnumerable<Product>> GetAllProductsAsync(int? categoryId = null, int? supplierId = null, 
        string? searchTerm = null, int skip = 0, int take = 100);

    /// <summary>
    /// Deletes a product
    /// </summary>
    /// <param name="id">The product identifier</param>
    /// <returns>True if the product was deleted, false otherwise</returns>
    Task<bool> DeleteProductAsync(int id);

    /// <summary>
    /// Searches products by name or SKU
    /// </summary>
    /// <param name="searchTerm">The search term</param>
    /// <returns>A collection of matching products</returns>
    Task<IEnumerable<Product>> SearchProductsAsync(string searchTerm);

    /// <summary>
    /// Gets products with low stock (below minimum stock level)
    /// </summary>
    /// <returns>A collection of products with low stock</returns>
    Task<IEnumerable<Product>> GetLowStockProductsAsync();

    /// <summary>
    /// Checks if a product exists
    /// </summary>
    /// <param name="id">The product identifier</param>
    /// <returns>True if the product exists, false otherwise</returns>
    Task<bool> ProductExistsAsync(int id);

    /// <summary>
    /// Gets a product by its SKU
    /// </summary>
    /// <param name="sku">The product SKU</param>
    /// <returns>The product if found, null otherwise</returns>
    Task<Product?> GetProductBySkuAsync(string sku);
}
