using microgest.Models;
using microgest.Enums;

namespace microgest.Services.Interfaces;

/// <summary>
/// Interface for stock service operations
/// </summary>
public interface IStockService
{
    /// <summary>
    /// Adds stock to a product
    /// </summary>
    /// <param name="productId">The product identifier</param>
    /// <param name="quantity">The quantity to add</param>
    /// <param name="reason">The reason for the stock addition</param>
    /// <param name="userId">The user performing the operation</param>
    /// <returns>The created stock movement</returns>
    Task<StockMovement> AddStockAsync(int productId, int quantity, string reason, string? userId = null);

    /// <summary>
    /// Removes stock from a product
    /// </summary>
    /// <param name="productId">The product identifier</param>
    /// <param name="quantity">The quantity to remove</param>
    /// <param name="reason">The reason for the stock removal</param>
    /// <param name="userId">The user performing the operation</param>
    /// <returns>The created stock movement</returns>
    Task<StockMovement> RemoveStockAsync(int productId, int quantity, string reason, string? userId = null);

    /// <summary>
    /// Adjusts stock for a product (can be positive or negative)
    /// </summary>
    /// <param name="productId">The product identifier</param>
    /// <param name="newQuantity">The new stock quantity</param>
    /// <param name="reason">The reason for the stock adjustment</param>
    /// <param name="userId">The user performing the operation</param>
    /// <returns>The created stock movement</returns>
    Task<StockMovement> AdjustStockAsync(int productId, int newQuantity, string reason, string? userId = null);

    /// <summary>
    /// Gets the current stock level for a product
    /// </summary>
    /// <param name="productId">The product identifier</param>
    /// <returns>The current stock level</returns>
    Task<int> GetStockLevelAsync(int productId);

    /// <summary>
    /// Gets the stock movement history for a product
    /// </summary>
    /// <param name="productId">The product identifier</param>
    /// <param name="skip">Number of records to skip</param>
    /// <param name="take">Number of records to take</param>
    /// <returns>A collection of stock movements</returns>
    Task<IEnumerable<StockMovement>> GetStockHistoryAsync(int productId, int skip = 0, int take = 100);

    /// <summary>
    /// Gets all stock movements within a date range
    /// </summary>
    /// <param name="startDate">The start date</param>
    /// <param name="endDate">The end date</param>
    /// <param name="movementType">Optional movement type filter</param>
    /// <returns>A collection of stock movements</returns>
    Task<IEnumerable<StockMovement>> GetStockMovementsByDateRangeAsync(DateTime startDate, DateTime endDate, 
        MovementType? movementType = null);

    /// <summary>
    /// Checks if there are products with low stock
    /// </summary>
    /// <returns>A collection of products with stock below minimum level</returns>
    Task<IEnumerable<Product>> CheckLowStockAsync();

    /// <summary>
    /// Validates if sufficient stock is available for removal
    /// </summary>
    /// <param name="productId">The product identifier</param>
    /// <param name="quantity">The quantity to check</param>
    /// <returns>True if sufficient stock is available, false otherwise</returns>
    Task<bool> IsStockAvailableAsync(int productId, int quantity);
}
