using microgest.Models;

namespace microgest.Services.Interfaces;

/// <summary>
/// Interface for supplier service operations
/// </summary>
public interface ISupplierService
{
    /// <summary>
    /// Creates a new supplier
    /// </summary>
    /// <param name="supplier">The supplier to create</param>
    /// <returns>The created supplier</returns>
    Task<Supplier> CreateSupplierAsync(Supplier supplier);

    /// <summary>
    /// Updates an existing supplier
    /// </summary>
    /// <param name="supplier">The supplier to update</param>
    /// <returns>The updated supplier</returns>
    Task<Supplier> UpdateSupplierAsync(Supplier supplier);

    /// <summary>
    /// Gets all suppliers
    /// </summary>
    /// <returns>A collection of all suppliers</returns>
    Task<IEnumerable<Supplier>> GetSuppliersAsync();

    /// <summary>
    /// Gets a supplier by its identifier
    /// </summary>
    /// <param name="id">The supplier identifier</param>
    /// <returns>The supplier if found, null otherwise</returns>
    Task<Supplier?> GetSupplierByIdAsync(int id);

    /// <summary>
    /// Deletes a supplier
    /// </summary>
    /// <param name="id">The supplier identifier</param>
    /// <returns>True if the supplier was deleted, false otherwise</returns>
    Task<bool> DeleteSupplierAsync(int id);

    /// <summary>
    /// Checks if a supplier exists
    /// </summary>
    /// <param name="id">The supplier identifier</param>
    /// <returns>True if the supplier exists, false otherwise</returns>
    Task<bool> SupplierExistsAsync(int id);
}
