using Microsoft.EntityFrameworkCore;
using microgest.Data;
using microgest.Models;
using microgest.Services.Interfaces;

namespace microgest.Services;

/// <summary>
/// Service for managing product operations
/// </summary>
public class ProductService : IProductService
{
    private readonly MicrogestDbContext _context;

    /// <summary>
    /// Initializes a new instance of the <see cref="ProductService"/> class
    /// </summary>
    /// <param name="context">The database context</param>
    public ProductService(MicrogestDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    /// <summary>
    /// Creates a new product
    /// </summary>
    /// <param name="product">The product to create</param>
    /// <returns>The created product</returns>
    /// <exception cref="ArgumentNullException">Thrown when product is null</exception>
    /// <exception cref="InvalidOperationException">Thrown when SKU already exists or category doesn't exist</exception>
    public async Task<Product> CreateProductAsync(Product product)
    {
        if (product == null)
            throw new ArgumentNullException(nameof(product));

        // Check if SKU already exists
        var existingProduct = await _context.Products
            .FirstOrDefaultAsync(p => p.SKU.ToLower() == product.SKU.ToLower());

        if (existingProduct != null)
            throw new InvalidOperationException($"A product with SKU '{product.SKU}' already exists.");

        // Validate category exists
        var categoryExists = await _context.Categories.AnyAsync(c => c.Id == product.CategoryId);
        if (!categoryExists)
            throw new InvalidOperationException($"Category with ID {product.CategoryId} does not exist.");

        // Validate supplier exists (if provided)
        if (product.SupplierId.HasValue)
        {
            var supplierExists = await _context.Suppliers.AnyAsync(s => s.Id == product.SupplierId.Value);
            if (!supplierExists)
                throw new InvalidOperationException($"Supplier with ID {product.SupplierId.Value} does not exist.");
        }

        _context.Products.Add(product);
        await _context.SaveChangesAsync();

        return await GetProductAsync(product.Id) ?? product;
    }

    /// <summary>
    /// Updates an existing product
    /// </summary>
    /// <param name="product">The product to update</param>
    /// <returns>The updated product</returns>
    /// <exception cref="ArgumentNullException">Thrown when product is null</exception>
    /// <exception cref="InvalidOperationException">Thrown when product is not found or validation fails</exception>
    public async Task<Product> UpdateProductAsync(Product product)
    {
        if (product == null)
            throw new ArgumentNullException(nameof(product));

        var existingProduct = await _context.Products.FindAsync(product.Id);
        if (existingProduct == null)
            throw new InvalidOperationException($"Product with ID {product.Id} not found.");

        // Check if another product with the same SKU exists
        var duplicateProduct = await _context.Products
            .FirstOrDefaultAsync(p => p.SKU.ToLower() == product.SKU.ToLower() && p.Id != product.Id);

        if (duplicateProduct != null)
            throw new InvalidOperationException($"A product with SKU '{product.SKU}' already exists.");

        // Validate category exists
        var categoryExists = await _context.Categories.AnyAsync(c => c.Id == product.CategoryId);
        if (!categoryExists)
            throw new InvalidOperationException($"Category with ID {product.CategoryId} does not exist.");

        // Validate supplier exists (if provided)
        if (product.SupplierId.HasValue)
        {
            var supplierExists = await _context.Suppliers.AnyAsync(s => s.Id == product.SupplierId.Value);
            if (!supplierExists)
                throw new InvalidOperationException($"Supplier with ID {product.SupplierId.Value} does not exist.");
        }

        existingProduct.Name = product.Name;
        existingProduct.Description = product.Description;
        existingProduct.SKU = product.SKU;
        existingProduct.CategoryId = product.CategoryId;
        existingProduct.UnitPrice = product.UnitPrice;
        existingProduct.MinimumStock = product.MinimumStock;
        existingProduct.CurrentStock = product.CurrentStock;
        existingProduct.SupplierId = product.SupplierId;

        await _context.SaveChangesAsync();

        return await GetProductAsync(existingProduct.Id) ?? existingProduct;
    }

    /// <summary>
    /// Gets a product by its identifier
    /// </summary>
    /// <param name="id">The product identifier</param>
    /// <returns>The product if found, null otherwise</returns>
    public async Task<Product?> GetProductAsync(int id)
    {
        return await _context.Products
            .Include(p => p.Category)
            .Include(p => p.Supplier)
            .FirstOrDefaultAsync(p => p.Id == id);
    }

    /// <summary>
    /// Gets all products with optional filtering and pagination
    /// </summary>
    /// <param name="categoryId">Optional category filter</param>
    /// <param name="supplierId">Optional supplier filter</param>
    /// <param name="searchTerm">Optional search term for name or SKU</param>
    /// <param name="skip">Number of records to skip</param>
    /// <param name="take">Number of records to take</param>
    /// <returns>A collection of products</returns>
    public async Task<IEnumerable<Product>> GetAllProductsAsync(int? categoryId = null, int? supplierId = null, 
        string? searchTerm = null, int skip = 0, int take = 100)
    {
        var query = _context.Products
            .Include(p => p.Category)
            .Include(p => p.Supplier)
            .AsQueryable();

        if (categoryId.HasValue)
            query = query.Where(p => p.CategoryId == categoryId.Value);

        if (supplierId.HasValue)
            query = query.Where(p => p.SupplierId == supplierId.Value);

        if (!string.IsNullOrEmpty(searchTerm))
        {
            var lowerSearchTerm = searchTerm.ToLower();
            query = query.Where(p => p.Name.ToLower().Contains(lowerSearchTerm) || 
                                   p.SKU.ToLower().Contains(lowerSearchTerm));
        }

        return await query
            .OrderBy(p => p.Name)
            .Skip(skip)
            .Take(take)
            .ToListAsync();
    }

    /// <summary>
    /// Deletes a product
    /// </summary>
    /// <param name="id">The product identifier</param>
    /// <returns>True if the product was deleted, false otherwise</returns>
    public async Task<bool> DeleteProductAsync(int id)
    {
        var product = await _context.Products.FindAsync(id);

        if (product == null)
            return false;

        // Note: Stock movements will be deleted due to cascade behavior
        _context.Products.Remove(product);
        await _context.SaveChangesAsync();

        return true;
    }

    /// <summary>
    /// Searches products by name or SKU
    /// </summary>
    /// <param name="searchTerm">The search term</param>
    /// <returns>A collection of matching products</returns>
    public async Task<IEnumerable<Product>> SearchProductsAsync(string searchTerm)
    {
        if (string.IsNullOrEmpty(searchTerm))
            return new List<Product>();

        var lowerSearchTerm = searchTerm.ToLower();

        return await _context.Products
            .Include(p => p.Category)
            .Include(p => p.Supplier)
            .Where(p => p.Name.ToLower().Contains(lowerSearchTerm) || 
                       p.SKU.ToLower().Contains(lowerSearchTerm))
            .OrderBy(p => p.Name)
            .ToListAsync();
    }

    /// <summary>
    /// Gets products with low stock (below minimum stock level)
    /// </summary>
    /// <returns>A collection of products with low stock</returns>
    public async Task<IEnumerable<Product>> GetLowStockProductsAsync()
    {
        return await _context.Products
            .Include(p => p.Category)
            .Include(p => p.Supplier)
            .Where(p => p.CurrentStock <= p.MinimumStock)
            .OrderBy(p => p.CurrentStock)
            .ToListAsync();
    }

    /// <summary>
    /// Checks if a product exists
    /// </summary>
    /// <param name="id">The product identifier</param>
    /// <returns>True if the product exists, false otherwise</returns>
    public async Task<bool> ProductExistsAsync(int id)
    {
        return await _context.Products.AnyAsync(p => p.Id == id);
    }

    /// <summary>
    /// Gets a product by its SKU
    /// </summary>
    /// <param name="sku">The product SKU</param>
    /// <returns>The product if found, null otherwise</returns>
    public async Task<Product?> GetProductBySkuAsync(string sku)
    {
        if (string.IsNullOrEmpty(sku))
            return null;

        return await _context.Products
            .Include(p => p.Category)
            .Include(p => p.Supplier)
            .FirstOrDefaultAsync(p => p.SKU.ToLower() == sku.ToLower());
    }
}
