using Microsoft.EntityFrameworkCore;
using microgest.Data;
using microgest.Models;
using microgest.Services.Interfaces;
using microgest.Enums;

namespace microgest.Services;

/// <summary>
/// Service for managing stock operations
/// </summary>
public class StockService : IStockService
{
    private readonly MicrogestDbContext _context;

    /// <summary>
    /// Initializes a new instance of the <see cref="StockService"/> class
    /// </summary>
    /// <param name="context">The database context</param>
    public StockService(MicrogestDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    /// <summary>
    /// Adds stock to a product
    /// </summary>
    /// <param name="productId">The product identifier</param>
    /// <param name="quantity">The quantity to add</param>
    /// <param name="reason">The reason for the stock addition</param>
    /// <param name="userId">The user performing the operation</param>
    /// <returns>The created stock movement</returns>
    /// <exception cref="ArgumentException">Thrown when quantity is not positive</exception>
    /// <exception cref="InvalidOperationException">Thrown when product is not found</exception>
    public async Task<StockMovement> AddStockAsync(int productId, int quantity, string reason, string? userId = null)
    {
        if (quantity <= 0)
            throw new ArgumentException("Quantity must be positive.", nameof(quantity));

        if (string.IsNullOrEmpty(reason))
            throw new ArgumentException("Reason cannot be null or empty.", nameof(reason));

        var product = await _context.Products.FindAsync(productId);
        if (product == null)
            throw new InvalidOperationException($"Product with ID {productId} not found.");

        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            // Update product stock
            product.CurrentStock += quantity;

            // Create stock movement record
            var stockMovement = new StockMovement
            {
                ProductId = productId,
                MovementType = MovementType.In,
                Quantity = quantity,
                Reason = reason,
                Date = DateTime.UtcNow,
                UserId = userId
            };

            _context.StockMovements.Add(stockMovement);
            await _context.SaveChangesAsync();

            await transaction.CommitAsync();
            return stockMovement;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    /// <summary>
    /// Removes stock from a product
    /// </summary>
    /// <param name="productId">The product identifier</param>
    /// <param name="quantity">The quantity to remove</param>
    /// <param name="reason">The reason for the stock removal</param>
    /// <param name="userId">The user performing the operation</param>
    /// <returns>The created stock movement</returns>
    /// <exception cref="ArgumentException">Thrown when quantity is not positive</exception>
    /// <exception cref="InvalidOperationException">Thrown when product is not found or insufficient stock</exception>
    public async Task<StockMovement> RemoveStockAsync(int productId, int quantity, string reason, string? userId = null)
    {
        if (quantity <= 0)
            throw new ArgumentException("Quantity must be positive.", nameof(quantity));

        if (string.IsNullOrEmpty(reason))
            throw new ArgumentException("Reason cannot be null or empty.", nameof(reason));

        var product = await _context.Products.FindAsync(productId);
        if (product == null)
            throw new InvalidOperationException($"Product with ID {productId} not found.");

        if (product.CurrentStock < quantity)
            throw new InvalidOperationException($"Insufficient stock. Available: {product.CurrentStock}, Requested: {quantity}");

        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            // Update product stock
            product.CurrentStock -= quantity;

            // Create stock movement record
            var stockMovement = new StockMovement
            {
                ProductId = productId,
                MovementType = MovementType.Out,
                Quantity = quantity,
                Reason = reason,
                Date = DateTime.UtcNow,
                UserId = userId
            };

            _context.StockMovements.Add(stockMovement);
            await _context.SaveChangesAsync();

            await transaction.CommitAsync();
            return stockMovement;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    /// <summary>
    /// Adjusts stock for a product (can be positive or negative)
    /// </summary>
    /// <param name="productId">The product identifier</param>
    /// <param name="newQuantity">The new stock quantity</param>
    /// <param name="reason">The reason for the stock adjustment</param>
    /// <param name="userId">The user performing the operation</param>
    /// <returns>The created stock movement</returns>
    /// <exception cref="ArgumentException">Thrown when new quantity is negative</exception>
    /// <exception cref="InvalidOperationException">Thrown when product is not found</exception>
    public async Task<StockMovement> AdjustStockAsync(int productId, int newQuantity, string reason, string? userId = null)
    {
        if (newQuantity < 0)
            throw new ArgumentException("New quantity cannot be negative.", nameof(newQuantity));

        if (string.IsNullOrEmpty(reason))
            throw new ArgumentException("Reason cannot be null or empty.", nameof(reason));

        var product = await _context.Products.FindAsync(productId);
        if (product == null)
            throw new InvalidOperationException($"Product with ID {productId} not found.");

        var adjustmentQuantity = Math.Abs(newQuantity - product.CurrentStock);
        if (adjustmentQuantity == 0)
            throw new InvalidOperationException("No adjustment needed. Current stock equals new quantity.");

        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            // Update product stock
            product.CurrentStock = newQuantity;

            // Create stock movement record
            var stockMovement = new StockMovement
            {
                ProductId = productId,
                MovementType = MovementType.Adjustment,
                Quantity = adjustmentQuantity,
                Reason = reason,
                Date = DateTime.UtcNow,
                UserId = userId
            };

            _context.StockMovements.Add(stockMovement);
            await _context.SaveChangesAsync();

            await transaction.CommitAsync();
            return stockMovement;
        }
        catch
        {
            await transaction.RollbackAsync();
            throw;
        }
    }

    /// <summary>
    /// Gets the current stock level for a product
    /// </summary>
    /// <param name="productId">The product identifier</param>
    /// <returns>The current stock level</returns>
    /// <exception cref="InvalidOperationException">Thrown when product is not found</exception>
    public async Task<int> GetStockLevelAsync(int productId)
    {
        var product = await _context.Products.FindAsync(productId);
        if (product == null)
            throw new InvalidOperationException($"Product with ID {productId} not found.");

        return product.CurrentStock;
    }

    /// <summary>
    /// Gets the stock movement history for a product
    /// </summary>
    /// <param name="productId">The product identifier</param>
    /// <param name="skip">Number of records to skip</param>
    /// <param name="take">Number of records to take</param>
    /// <returns>A collection of stock movements</returns>
    public async Task<IEnumerable<StockMovement>> GetStockHistoryAsync(int productId, int skip = 0, int take = 100)
    {
        return await _context.StockMovements
            .Include(sm => sm.Product)
            .Where(sm => sm.ProductId == productId)
            .OrderByDescending(sm => sm.Date)
            .Skip(skip)
            .Take(take)
            .ToListAsync();
    }

    /// <summary>
    /// Gets all stock movements within a date range
    /// </summary>
    /// <param name="startDate">The start date</param>
    /// <param name="endDate">The end date</param>
    /// <param name="movementType">Optional movement type filter</param>
    /// <returns>A collection of stock movements</returns>
    public async Task<IEnumerable<StockMovement>> GetStockMovementsByDateRangeAsync(DateTime startDate, DateTime endDate, 
        MovementType? movementType = null)
    {
        var query = _context.StockMovements
            .Include(sm => sm.Product)
            .Where(sm => sm.Date >= startDate && sm.Date <= endDate);

        if (movementType.HasValue)
            query = query.Where(sm => sm.MovementType == movementType.Value);

        return await query
            .OrderByDescending(sm => sm.Date)
            .ToListAsync();
    }

    /// <summary>
    /// Checks if there are products with low stock
    /// </summary>
    /// <returns>A collection of products with stock below minimum level</returns>
    public async Task<IEnumerable<Product>> CheckLowStockAsync()
    {
        return await _context.Products
            .Include(p => p.Category)
            .Include(p => p.Supplier)
            .Where(p => p.CurrentStock <= p.MinimumStock)
            .OrderBy(p => p.CurrentStock)
            .ToListAsync();
    }

    /// <summary>
    /// Validates if sufficient stock is available for removal
    /// </summary>
    /// <param name="productId">The product identifier</param>
    /// <param name="quantity">The quantity to check</param>
    /// <returns>True if sufficient stock is available, false otherwise</returns>
    public async Task<bool> IsStockAvailableAsync(int productId, int quantity)
    {
        var product = await _context.Products.FindAsync(productId);
        return product != null && product.CurrentStock >= quantity;
    }
}
