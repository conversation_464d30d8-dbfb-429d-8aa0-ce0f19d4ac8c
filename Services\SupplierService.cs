using Microsoft.EntityFrameworkCore;
using microgest.Data;
using microgest.Models;
using microgest.Services.Interfaces;

namespace microgest.Services;

/// <summary>
/// Service for managing supplier operations
/// </summary>
public class SupplierService : ISupplierService
{
    private readonly MicrogestDbContext _context;

    /// <summary>
    /// Initializes a new instance of the <see cref="SupplierService"/> class
    /// </summary>
    /// <param name="context">The database context</param>
    public SupplierService(MicrogestDbContext context)
    {
        _context = context ?? throw new ArgumentNullException(nameof(context));
    }

    /// <summary>
    /// Creates a new supplier
    /// </summary>
    /// <param name="supplier">The supplier to create</param>
    /// <returns>The created supplier</returns>
    /// <exception cref="ArgumentNullException">Thrown when supplier is null</exception>
    /// <exception cref="InvalidOperationException">Thrown when a supplier with the same name or email already exists</exception>
    public async Task<Supplier> CreateSupplierAsync(Supplier supplier)
    {
        if (supplier == null)
            throw new ArgumentNullException(nameof(supplier));

        // Check if supplier with same name already exists
        var existingSupplier = await _context.Suppliers
            .FirstOrDefaultAsync(s => s.Name.ToLower() == supplier.Name.ToLower());

        if (existingSupplier != null)
            throw new InvalidOperationException($"A supplier with the name '{supplier.Name}' already exists.");

        // Check if supplier with same email already exists (if email is provided)
        if (!string.IsNullOrEmpty(supplier.Email))
        {
            var existingEmailSupplier = await _context.Suppliers
                .FirstOrDefaultAsync(s => s.Email!.ToLower() == supplier.Email.ToLower());

            if (existingEmailSupplier != null)
                throw new InvalidOperationException($"A supplier with the email '{supplier.Email}' already exists.");
        }

        _context.Suppliers.Add(supplier);
        await _context.SaveChangesAsync();

        return supplier;
    }

    /// <summary>
    /// Updates an existing supplier
    /// </summary>
    /// <param name="supplier">The supplier to update</param>
    /// <returns>The updated supplier</returns>
    /// <exception cref="ArgumentNullException">Thrown when supplier is null</exception>
    /// <exception cref="InvalidOperationException">Thrown when supplier is not found or conflicts exist</exception>
    public async Task<Supplier> UpdateSupplierAsync(Supplier supplier)
    {
        if (supplier == null)
            throw new ArgumentNullException(nameof(supplier));

        var existingSupplier = await _context.Suppliers.FindAsync(supplier.Id);
        if (existingSupplier == null)
            throw new InvalidOperationException($"Supplier with ID {supplier.Id} not found.");

        // Check if another supplier with the same name exists
        var duplicateSupplier = await _context.Suppliers
            .FirstOrDefaultAsync(s => s.Name.ToLower() == supplier.Name.ToLower() && s.Id != supplier.Id);

        if (duplicateSupplier != null)
            throw new InvalidOperationException($"A supplier with the name '{supplier.Name}' already exists.");

        // Check if another supplier with the same email exists (if email is provided)
        if (!string.IsNullOrEmpty(supplier.Email))
        {
            var duplicateEmailSupplier = await _context.Suppliers
                .FirstOrDefaultAsync(s => s.Email!.ToLower() == supplier.Email.ToLower() && s.Id != supplier.Id);

            if (duplicateEmailSupplier != null)
                throw new InvalidOperationException($"A supplier with the email '{supplier.Email}' already exists.");
        }

        existingSupplier.Name = supplier.Name;
        existingSupplier.ContactInfo = supplier.ContactInfo;
        existingSupplier.Email = supplier.Email;
        existingSupplier.Phone = supplier.Phone;

        await _context.SaveChangesAsync();

        return existingSupplier;
    }

    /// <summary>
    /// Gets all suppliers
    /// </summary>
    /// <returns>A collection of all suppliers</returns>
    public async Task<IEnumerable<Supplier>> GetSuppliersAsync()
    {
        return await _context.Suppliers
            .OrderBy(s => s.Name)
            .ToListAsync();
    }

    /// <summary>
    /// Gets a supplier by its identifier
    /// </summary>
    /// <param name="id">The supplier identifier</param>
    /// <returns>The supplier if found, null otherwise</returns>
    public async Task<Supplier?> GetSupplierByIdAsync(int id)
    {
        return await _context.Suppliers
            .Include(s => s.Products)
            .FirstOrDefaultAsync(s => s.Id == id);
    }

    /// <summary>
    /// Deletes a supplier
    /// </summary>
    /// <param name="id">The supplier identifier</param>
    /// <returns>True if the supplier was deleted, false otherwise</returns>
    public async Task<bool> DeleteSupplierAsync(int id)
    {
        var supplier = await _context.Suppliers.FindAsync(id);

        if (supplier == null)
            return false;

        // Note: Products will have their SupplierId set to null due to cascade behavior
        _context.Suppliers.Remove(supplier);
        await _context.SaveChangesAsync();

        return true;
    }

    /// <summary>
    /// Checks if a supplier exists
    /// </summary>
    /// <param name="id">The supplier identifier</param>
    /// <returns>True if the supplier exists, false otherwise</returns>
    public async Task<bool> SupplierExistsAsync(int id)
    {
        return await _context.Suppliers.AnyAsync(s => s.Id == id);
    }
}
